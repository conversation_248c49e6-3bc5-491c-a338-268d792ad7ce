/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-wrapper {
    display: flex;
    width: 100%;
    min-height: 80px;
}

.logo-section {
    background: white;
    padding: 20px 40px;
    display: flex;
    align-items: center;
    flex: 0 0 auto;
}

.logo h1 {
    color: #1a365d;
    font-size: 1.4rem;
    font-weight: 700;
    line-height: 1.2;
    text-transform: lowercase;
    margin: 0;
}

.nav-section {
    background: #fbbf24;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 35px;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: #1a365d;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #2563eb;
}

.header-book-btn {
    background: #000000;
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.header-book-btn:hover {
    background: #333333;
    transform: translateY(-2px);
}

.mobile-menu-btn {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-btn span {
    width: 25px;
    height: 3px;
    background: #1a365d;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.3)),
                url('https://nontradecementmanufacturing.com/wp-content/uploads/2025/03/Untitled-design-5-1-1024x640.webp') center/cover;
    z-index: -1;
}

.hero-content-wrapper {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

.hero-content {
    max-width: 600px;
    color: white;
    margin-bottom: 40px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 0;
    opacity: 0.95;
}

.hero-button-wrapper {
    position: absolute;
    bottom: 40px;
    left: 40px;
}

.hero-book-btn {
    background: #f97316;
    color: white;
    padding: 16px 32px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-book-btn:hover {
    background: #ea580c;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(249, 115, 22, 0.4);
}

/* About Section */
.about {
    padding: 80px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    margin-bottom: 80px;
}

.section-label {
    color: #f59e0b;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 16px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a365d;
    line-height: 1.2;
    margin-bottom: 24px;
}

.section-description {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.7;
    margin-bottom: 32px;
}

.learn-more {
    color: #f59e0b;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    border-bottom: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.learn-more:hover {
    border-bottom-color: #f59e0b;
}

.about-image {
    display: flex;
    justify-content: center;
}

.image-placeholder img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.values-section {
    margin-bottom: 80px;
}

.values-content {
    text-align: center;
    margin-bottom: 60px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.vision-mission {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 60px;
}

.vision-item {
    text-align: center;
    padding: 30px;
    background: #f8fafc;
    border-radius: 12px;
}

.vision-item h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a365d;
    margin-bottom: 16px;
}

.vision-item p {
    color: #666;
    line-height: 1.6;
}

.cta-section {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    border-radius: 16px;
    padding: 60px 40px;
    text-align: center;
    color: white;
}

.cta-section h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
    line-height: 1.3;
}

.cta-section p {
    font-size: 1.1rem;
    margin-bottom: 32px;
    opacity: 0.9;
}

.cta-btn {
    background: white;
    color: #2563eb;
    padding: 16px 32px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255,255,255,0.3);
}

/* Products Section */
.products {
    padding: 80px 0;
    background: #f8fafc;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.product-image {
    height: 200px;
    background: linear-gradient(135deg, #64748b, #475569);
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-placeholder {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
}

.product-content {
    padding: 30px;
}

.product-content h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #1a365d;
    margin-bottom: 16px;
}

.product-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
}

.product-btn {
    background: #f59e0b;
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: inline-block;
}

.product-btn:hover {
    background: #d97706;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-wrapper {
        flex-direction: column;
    }

    .logo-section {
        padding: 15px 20px;
    }

    .nav-section {
        padding: 15px 20px;
        justify-content: space-between;
    }

    .nav {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .hero-content-wrapper {
        padding: 0 20px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-button-wrapper {
        left: 20px;
        bottom: 20px;
    }

    .about-content,
    .vision-mission {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .section-title {
        font-size: 2rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .cta-section {
        padding: 40px 20px;
    }

    .cta-section h2 {
        font-size: 1.8rem;
    }
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 80px 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
}

.feature-card {
    text-align: center;
    padding: 40px 30px;
    background: #f8fafc;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    background: white;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 2rem;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a365d;
    margin-bottom: 16px;
    line-height: 1.3;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: #f8fafc;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.contact-item h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a365d;
    margin-bottom: 12px;
}

.contact-item p {
    color: #666;
    line-height: 1.6;
}

.contact-form {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.contact-form h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #1a365d;
    margin-bottom: 30px;
    text-align: center;
}

.contact-form form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-form input,
.contact-form textarea {
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.contact-form button {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.contact-form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

/* Footer */
.footer {
    background: #1a365d;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
    text-transform: lowercase;
}

.footer-info p {
    color: #cbd5e1;
    line-height: 1.6;
    max-width: 400px;
}

.footer-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.link-column h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.link-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.link-column li {
    margin-bottom: 12px;
}

.link-column a {
    color: #cbd5e1;
    text-decoration: none;
    transition: color 0.3s ease;
}

.link-column a:hover {
    color: #f59e0b;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom p {
    color: #cbd5e1;
    font-size: 0.9rem;
}

.footer-bottom a {
    color: #cbd5e1;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom a:hover {
    color: white;
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .feature-card {
        padding: 30px 20px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}
